#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四阶段：近实时火灾检测
基于论文"Coupling Physical Model and Deep Learning for Near Real-Time Wildfire Detection"的完整实现

主要功能：
1. 加载第三阶段生成的BRDF-t预测图像
2. 获取GOES-17观测数据
3. 计算观测值与理论预测值的偏差
4. 基于偏差阈值进行火灾检测
5. 执行时空匹配验证分析
"""

import os
import sys
import csv
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from datetime import datetime, timedelta
import ee
from math import radians, cos, sin, asin, sqrt
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图像生成功能将被禁用")

# 初始化Google Earth Engine
try:
    ee.Initialize(project="my-earth-engine-work-466614", opt_url='https://earthengine.googleapis.com')
    print("✓ Google Earth Engine 初始化成功")
except Exception as e:
    print(f"✗ Google Earth Engine 初始化失败: {e}")
    sys.exit(1)

# 研究区域边界（华盛顿州，根据论文）
AOI_BOUNDS = {
    'west': -124.20,
    'east': -118.27,
    'south': 45.24,
    'north': 49.77
}

# 火灾检测参数（根据论文原文）
FIRE_DETECTION_THRESHOLD = 1500  # 偏差阈值（论文原文中的DN值阈值）
SPATIAL_BUFFER_KM = 2.5  # 空间缓冲区半径（公里）
TIME_WINDOW_MINUTES = [-40, 20]  # 时间窗口（分钟）

# GOES数据处理参数（根据论文）
GOES_DN_SCALE_FACTOR = 1000.0  # GOES CMI_C02波段DN值缩放因子
GOES_REFLECTANCE_SCALE = 0.0001  # GOES反射率缩放因子（从DN转换为反射率）
PREDICTION_VALUE_SCALE = 10000.0  # 预测值的缩放因子（与MODIS BRDF一致）

# 模型文件路径
MODEL_PATHS = {
    'cross_scale': 'cross_scale_model_best.pth',
    'convlstm_base': 'convlstm_model_{}_best.pth'
}

def load_models(device):
    """
    加载训练好的模型

    Returns:
        dict: 包含加载的模型的字典
    """
    models = {'cross_scale': None, 'convlstm_models': {}}

    try:
        # 加载跨尺度模型（从第三阶段）
        if os.path.exists(MODEL_PATHS['cross_scale']):
            try:
                from stage3_deep_learning import CrossScaleTransformModel
                cross_scale_model = CrossScaleTransformModel(input_size=1, hidden_size=128, output_size=1)
                cross_scale_model.load_state_dict(torch.load(MODEL_PATHS['cross_scale'], map_location=device))
                cross_scale_model.to(device)
                cross_scale_model.eval()
                models['cross_scale'] = cross_scale_model
                print("✓ 跨尺度转换模型加载成功")
            except Exception as e:
                print(f"⚠ 跨尺度模型加载失败: {e}")
                # 使用第二阶段的物理模型作为替代
                models['cross_scale'] = 'physical_model'  # 标记使用物理模型
                print("✓ 使用第二阶段物理模型作为跨尺度转换")
        else:
            print("⚠ 跨尺度模型文件不存在，使用物理模型")
            models['cross_scale'] = 'physical_model'
            print("✓ 使用第二阶段物理模型作为跨尺度转换")

        # 加载ConvLSTM模型（按地表类型）
        for lc_type in range(1, 18):  # MODIS地表类型1-17
            model_path = MODEL_PATHS['convlstm_base'].format(lc_type)
            if os.path.exists(model_path):
                from stage3_deep_learning import ConvLSTMModel
                convlstm_model = ConvLSTMModel(input_dim=1, hidden_dim=32, num_layers=2)
                convlstm_model.load_state_dict(torch.load(model_path, map_location=device))
                convlstm_model.to(device)
                convlstm_model.eval()
                models['convlstm_models'][lc_type] = convlstm_model

        print(f"✓ 加载了 {len(models['convlstm_models'])} 个ConvLSTM模型")
        return models

    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return models

def find_latest_stage3_asset():
    """
    查找最新的第三阶段预测图像Asset

    Returns:
        str: Asset ID，如果未找到则返回None
    """
    try:
        # 获取用户的Asset列表
        assets = ee.data.listAssets({'parent': 'projects/my-earth-engine-work-466614/assets'})

        # 筛选第三阶段预测图像（包括第二阶段生成的虚拟影像集）
        prediction_assets = []
        for asset in assets.get('assets', []):
            asset_id = asset['name']
            # 查找可能的预测图像Asset
            if any(keyword in asset_id for keyword in [
                'stage3_predictions', 'brdf_predictions', 'low_res_collection',
                'virtual_reflectance_collection', 'prediction_collection'
            ]):
                prediction_assets.append({
                    'id': asset_id,
                    'time': asset.get('updateTime', asset.get('createTime', ''))
                })

        if not prediction_assets:
            return None

        # 按时间排序，返回最新的
        prediction_assets.sort(key=lambda x: x['time'], reverse=True)
        latest_asset = prediction_assets[0]['id']
        print(f"✓ 找到最新的第三阶段预测图像: {latest_asset}")
        return latest_asset

    except Exception as e:
        print(f"✗ 查找第三阶段Asset失败: {e}")
        return None

def load_stage3_prediction_data():
    """
    加载第三阶段生成的预测数据（1008个时间点的影像集合）

    Returns:
        ee.ImageCollection: 预测影像集合
    """
    try:
        # 尝试从文件读取Asset ID
        asset_id = None
        try:
            with open('low_res_asset_id.txt', 'r') as f:
                asset_id = f.read().strip()
            print(f"从文件读取Asset ID: {asset_id}")
        except FileNotFoundError:
            print("未找到Asset ID文件，尝试自动查找...")
            asset_id = find_latest_stage3_asset()
            if not asset_id:
                print("使用手动指定的Asset ID")
                asset_id = 'projects/my-earth-engine-work-466614/assets/low_res_collection_20250803_180229'

        # 加载多波段影像（1008个波段代表1008个时间点）
        multi_band_image = ee.Image(asset_id)

        # 验证影像
        band_names = multi_band_image.bandNames().getInfo()
        print(f"✓ 成功加载预测影像，包含 {len(band_names)} 个时间点")

        # 将多波段影像转换为影像集合
        print("正在将多波段影像转换为时间序列影像集合...")
        image_list = []

        # 创建时间序列（从2021-07-19开始，每10分钟一个时间点）
        start_time = datetime(2021, 7, 19, 0, 0, 0)

        for i, band_name in enumerate(band_names):
            # 选择单个波段
            single_band_image = multi_band_image.select([band_name]).rename('predicted_reflectance')

            # 计算对应的时间
            image_time = start_time + timedelta(minutes=i * 10)
            time_millis = int(image_time.timestamp() * 1000)

            # 添加时间属性
            single_band_image = single_band_image.set({
                'system:time_start': time_millis,
                'time_index': i,
                'datetime': image_time.isoformat()
            })

            image_list.append(single_band_image)

        # 创建影像集合
        prediction_collection = ee.ImageCollection(image_list)

        print(f"✓ 预测影像集合创建完成，包含 {len(image_list)} 个时间点")

        return prediction_collection

    except Exception as e:
        print(f"✗ 加载第三阶段预测数据失败: {e}")
        return None

def get_prediction_for_time(prediction_collection, target_time):
    """
    获取指定时间的预测影像

    Args:
        prediction_collection: 预测影像集合
        target_time: 目标时间

    Returns:
        ee.Image: 对应时间的预测影像
    """
    try:
        # 将目标时间转换为毫秒
        target_millis = int(target_time.timestamp() * 1000)

        # 查找最接近的时间点
        filtered_collection = prediction_collection.filter(
            ee.Filter.eq('system:time_start', target_millis)
        )

        collection_size = filtered_collection.size().getInfo()

        if collection_size > 0:
            return filtered_collection.first()
        else:
            # 如果没有精确匹配，找最接近的时间点
            print(f"未找到精确时间匹配，查找最接近的时间点...")

            # 计算时间差并找到最小值
            def add_time_diff(image):
                image_time = ee.Number(image.get('system:time_start'))
                time_diff = ee.Number(target_millis).subtract(image_time).abs()
                return image.set('time_diff', time_diff)

            collection_with_diff = prediction_collection.map(add_time_diff)
            closest_image = collection_with_diff.sort('time_diff').first()

            return closest_image.select('predicted_reflectance')

    except Exception as e:
        print(f"⚠ 获取预测影像失败: {e}")
        return None

def get_goes_observation(target_time, aoi):
    """
    获取GOES-17观测数据

    Args:
        target_time: 目标时间
        aoi: 研究区域

    Returns:
        ee.Image: GOES-17图像，如果失败返回None
    """
    try:
        # 定义时间窗口
        start_time = target_time - timedelta(minutes=5)
        end_time = target_time + timedelta(minutes=5)

        # 获取GOES-17数据
        goes_collection = (ee.ImageCollection('NOAA/GOES/17/MCMIPC')
                          .filterDate(start_time.isoformat(), end_time.isoformat())
                          .filterBounds(aoi))

        collection_size = goes_collection.size().getInfo()

        if collection_size == 0:
            # 扩大时间窗口
            start_time = target_time - timedelta(minutes=30)
            end_time = target_time + timedelta(minutes=30)
            goes_collection = (ee.ImageCollection('NOAA/GOES/17/MCMIPC')
                              .filterDate(start_time.isoformat(), end_time.isoformat())
                              .filterBounds(aoi))
            collection_size = goes_collection.size().getInfo()

        if collection_size > 0:
            # 选择最接近目标时间的图像
            goes_image = goes_collection.first()

            # 检查可用波段
            band_names = goes_image.bandNames().getInfo()
            if 'CMI_C02' in band_names:
                return goes_image.select(['CMI_C02'])  # 选择可见光波段
            elif band_names:
                # 使用第一个可用波段
                return goes_image.select([band_names[0]])
            else:
                return None
        else:
            return None

    except Exception as e:
        print(f"✗ 获取GOES观测数据失败: {e}")
        return None

def calculate_solar_geometry(coords, time):
    """
    计算太阳几何参数

    Args:
        coords: 坐标 [lon, lat]
        time: 时间

    Returns:
        tuple: (太阳天顶角, 太阳方位角)
    """
    try:
        import math

        _, lat = coords

        # 简化的太阳几何计算
        day_of_year = time.timetuple().tm_yday
        hour = time.hour + time.minute / 60.0

        # 太阳赤纬角
        declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))

        # 时角
        hour_angle = 15 * (hour - 12)

        # 太阳高度角
        lat_rad = math.radians(lat)
        dec_rad = math.radians(declination)
        hour_rad = math.radians(hour_angle)

        elevation = math.asin(
            math.sin(lat_rad) * math.sin(dec_rad) +
            math.cos(lat_rad) * math.cos(dec_rad) * math.cos(hour_rad)
        )

        # 太阳天顶角
        zenith = math.pi / 2 - elevation

        # 太阳方位角（简化计算）
        azimuth = math.atan2(
            math.sin(hour_rad),
            math.cos(hour_rad) * math.sin(lat_rad) - math.tan(dec_rad) * math.cos(lat_rad)
        )

        return zenith, azimuth

    except Exception:
        # 如果计算失败，返回默认值
        return 0.5, 3.14

def extract_pixel_time_series(prediction_image, coords, modis_lc):
    """
    从预测图像中提取像素时间序列

    Args:
        prediction_image: 预测图像
        coords: 坐标 [lon, lat]
        modis_lc: MODIS地表覆盖数据

    Returns:
        tuple: (时间序列数据, 地表类型)
    """
    try:
        point = ee.Geometry.Point(coords)

        # 获取地表类型
        lc_value = modis_lc.reduceRegion(
            reducer=ee.Reducer.first(),
            geometry=point,
            scale=500,
            maxPixels=1e6
        ).getInfo()

        lc_type = lc_value.get('LC_Type1')
        if lc_type is None or lc_type == 0:
            return None, None

        # 提取时间序列
        pixel_values = prediction_image.reduceRegion(
            reducer=ee.Reducer.first(),
            geometry=point,
            scale=500,
            maxPixels=1e6
        ).getInfo()

        # 提取所有波段的值
        band_names = prediction_image.bandNames().getInfo()
        time_series = []

        for band in band_names:
            value = pixel_values.get(band)
            if value is not None:
                time_series.append(float(value))

        if len(time_series) >= 5:  # 至少需要5个时间点
            return time_series, int(lc_type)
        else:
            return None, None

    except Exception:
        return None, None

def predict_theoretical_value(time_series, lc_type, models, device, coords, target_time):
    """
    预测理论观测值

    Args:
        time_series: 历史时间序列
        lc_type: 地表类型
        models: 模型字典
        device: 计算设备
        coords: 坐标
        target_time: 目标时间

    Returns:
        float: 预测的理论值，失败返回None
    """
    try:
        # 使用ConvLSTM模型进行基础预测
        if lc_type not in models['convlstm_models']:
            return None

        convlstm_model = models['convlstm_models'][lc_type]

        # 准备输入数据
        sequence_length = min(len(time_series), 144)  # 最多使用144个时间点（1天）
        input_sequence = time_series[-sequence_length:]

        # 转换为张量
        input_tensor = torch.FloatTensor(input_sequence).unsqueeze(0).unsqueeze(0).unsqueeze(-1).unsqueeze(-1).to(device)

        with torch.no_grad():
            base_prediction = convlstm_model(input_tensor)
            base_value = base_prediction.cpu().item()

        # 如果有跨尺度模型，应用BRDF校正
        if models['cross_scale'] is not None:
            try:
                solar_zenith, solar_azimuth = calculate_solar_geometry(coords, target_time)
                geometry_tensor = torch.FloatTensor([solar_zenith, solar_azimuth]).unsqueeze(0).to(device)

                with torch.no_grad():
                    combined_input = torch.cat([
                        torch.FloatTensor([base_value]).unsqueeze(0).to(device),
                        geometry_tensor
                    ], dim=1)

                    corrected_prediction = models['cross_scale'](combined_input)
                    return corrected_prediction.cpu().item()
            except Exception:
                return base_value
        else:
            return base_value

    except Exception:
        return None

def get_pixel_value(image, coords, band_name=None, return_type='dn'):
    """
    获取指定坐标的像素值（根据论文方法）

    Args:
        image: ee.Image对象
        coords: 坐标 [lon, lat]
        band_name: 波段名称
        return_type: 返回值类型 ('dn'=原始DN值, 'reflectance'=反射率, 'scaled'=缩放值)

    Returns:
        float: 像素值，失败返回None
    """
    try:
        point = ee.Geometry.Point(coords)

        if band_name:
            image = image.select([band_name])

        pixel_value = image.reduceRegion(
            reducer=ee.Reducer.first(),
            geometry=point,
            scale=2000,  # 使用GOES分辨率
            maxPixels=1e6
        ).getInfo()

        # 获取第一个波段的值
        for _, value in pixel_value.items():
            if value is not None:
                pixel_val = float(value)

                if return_type == 'dn':
                    # 返回原始DN值（用于火灾检测）
                    return pixel_val
                elif return_type == 'reflectance':
                    # 转换为反射率（根据GOES产品规范）
                    if pixel_val > 1.0:  # 原始DN值
                        reflectance = pixel_val * GOES_REFLECTANCE_SCALE  # 0.0001缩放因子
                        return max(0.0, min(1.0, reflectance))
                    else:
                        return pixel_val  # 已经是反射率
                elif return_type == 'scaled':
                    # 返回缩放到预测值范围的值（用于模型比较）
                    if pixel_val > 1.0:  # 原始DN值
                        scaled_val = pixel_val / GOES_DN_SCALE_FACTOR  # 缩放到0-1范围
                        return max(0.0, min(1.0, scaled_val))
                    else:
                        return pixel_val

        return None

    except Exception:
        return None

def get_batch_pixel_values(image, coords_list, return_type='dn'):
    """
    批量获取像素值以提高性能（根据论文方法）

    Args:
        image: GEE图像
        coords_list: 坐标列表 [[lon, lat], ...]
        return_type: 返回值类型 ('dn'=原始DN值, 'reflectance'=反射率, 'scaled'=缩放值)

    Returns:
        dict: {index: pixel_value}
    """
    try:
        # 创建点集合
        points = [ee.Geometry.Point(coords) for coords in coords_list]
        points_collection = ee.FeatureCollection([ee.Feature(point) for point in points])

        # 批量采样
        sampled = image.sampleRegions(
            collection=points_collection,
            scale=2000,
            geometries=True
        )

        # 获取结果
        features = sampled.getInfo()['features']
        results = {}

        for i, feature in enumerate(features):
            properties = feature.get('properties', {})
            # 获取第一个非系统属性作为像素值
            for key, value in properties.items():
                if not key.startswith('system:') and value is not None:
                    pixel_value = float(value)

                    if return_type == 'dn':
                        # 返回原始DN值（用于火灾检测）
                        results[i] = pixel_value
                    elif return_type == 'reflectance':
                        # 转换为反射率
                        if pixel_value > 1.0:  # 原始DN值
                            reflectance = pixel_value * GOES_REFLECTANCE_SCALE
                            results[i] = max(0.0, min(1.0, reflectance))
                        else:
                            results[i] = pixel_value
                    elif return_type == 'scaled':
                        # 缩放到预测值范围
                        if pixel_value > 1.0:  # 原始DN值
                            scaled_value = pixel_value / GOES_DN_SCALE_FACTOR
                            results[i] = max(0.0, min(1.0, scaled_value))
                        else:
                            results[i] = pixel_value
                    break

        return results

    except Exception:
        # 如果批量处理失败，回退到逐个处理
        results = {}
        for i, coords in enumerate(coords_list):
            value = get_pixel_value(image, coords, return_type=return_type)
            if value is not None:
                results[i] = value
        return results

def get_batch_predictions(prediction_image, coords_list):
    """
    批量获取预测值（简化版本，直接从预测图像获取）

    Args:
        prediction_image: 预测图像（第三阶段生成的BRDF-t预测结果）
        coords_list: 坐标列表

    Returns:
        dict: {index: predicted_value}
    """
    try:
        # 直接从预测图像批量采样
        points = [ee.Geometry.Point(coords) for coords in coords_list]
        points_collection = ee.FeatureCollection([ee.Feature(point) for point in points])

        # 批量采样预测图像
        sampled = prediction_image.sampleRegions(
            collection=points_collection,
            scale=500,  # 使用MODIS分辨率
            geometries=True
        )

        features = sampled.getInfo()['features']
        results = {}

        for i, feature in enumerate(features):
            try:
                properties = feature.get('properties', {})
                # 获取预测值（应该是反射率范围0-1）
                for key, value in properties.items():
                    if not key.startswith('system:') and value is not None:
                        predicted_value = float(value)

                        # 确保预测值在合理范围内
                        if predicted_value > 1.0:
                            # 如果值大于1，可能是缩放后的值，需要归一化
                            predicted_value = predicted_value / PREDICTION_VALUE_SCALE

                        # 限制在0-1范围
                        predicted_value = max(0.0, min(1.0, predicted_value))
                        results[i] = predicted_value
                        break

            except Exception:
                continue

        return results

    except Exception:
        # 如果批量处理失败，回退到逐个处理
        results = {}
        for i, coords in enumerate(coords_list):
            try:
                point = ee.Geometry.Point(coords)
                pixel_value = prediction_image.reduceRegion(
                    reducer=ee.Reducer.first(),
                    geometry=point,
                    scale=500,
                    maxPixels=1e6
                ).getInfo()

                for _, value in pixel_value.items():
                    if value is not None:
                        predicted_value = float(value)
                        if predicted_value > 1.0:
                            predicted_value = predicted_value / PREDICTION_VALUE_SCALE
                        predicted_value = max(0.0, min(1.0, predicted_value))
                        results[i] = predicted_value
                        break
            except Exception:
                continue

        return results

def perform_fire_detection(target_time):
    """
    执行火灾检测（根据论文方法）

    Args:
        target_time: 检测时间

    Returns:
        tuple: (火点列表, 预测数据)
    """
    print(f"\n=== 开始火灾检测: {target_time} ===")

    # 定义研究区域
    aoi = ee.Geometry.Rectangle([
        AOI_BOUNDS['west'], AOI_BOUNDS['south'],
        AOI_BOUNDS['east'], AOI_BOUNDS['north']
    ])

    # 1. 获取GOES-17观测数据
    print("1. 获取GOES-17观测数据...")
    goes_image = get_goes_observation(target_time, aoi)
    if goes_image is None:
        print("✗ 无法获取GOES-17观测数据")
        return [], {'observed': [], 'predicted': []}

    # 2. 加载第三阶段预测数据集合
    print("2. 加载第三阶段预测数据集合...")
    prediction_collection = load_stage3_prediction_data()
    if prediction_collection is None:
        print("✗ 无法加载第三阶段预测数据")
        return [], {'observed': [], 'predicted': []}

    # 3. 获取目标时间的预测影像
    print("3. 获取目标时间的预测影像...")
    prediction_image = get_prediction_for_time(prediction_collection, target_time)
    if prediction_image is None:
        print("✗ 无法获取目标时间的预测影像")
        return [], {'observed': [], 'predicted': []}

    # 4. 生成优化的采样网格（根据论文，使用较密的网格）
    print("4. 生成采样网格...")
    sample_points = generate_sample_grid(grid_size_km=5)  # 使用5公里网格确保检测精度
    print(f"生成了 {len(sample_points)} 个采样点")

    # 5. 执行火灾检测（根据论文方法）
    print("5. 执行火灾检测...")
    fire_points = []
    prediction_data = {'observed': [], 'predicted': []}

    # 批量处理以提高效率
    batch_size = min(100, len(sample_points))  # 每批处理100个点
    total_batches = (len(sample_points) + batch_size - 1) // batch_size

    stats = {
        'total_points': len(sample_points),
        'valid_points': 0,
        'successful_predictions': 0,
        'deviations': []
    }

    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(sample_points))
        batch_points = sample_points[start_idx:end_idx]

        print(f"  处理批次 {batch_idx + 1}/{total_batches} ({len(batch_points)} 个点)")

        # 批量获取GOES观测值（原始DN值，用于火灾检测）
        batch_observed_dn = get_batch_pixel_values(goes_image, batch_points, return_type='dn')

        # 批量获取GOES观测值（反射率，用于显示）
        batch_observed_ref = get_batch_pixel_values(goes_image, batch_points, return_type='reflectance')

        # 批量获取预测值（从第三阶段预测影像）
        batch_predictions = get_batch_predictions(prediction_image, batch_points)

        # 处理批次结果
        for i, point_coords in enumerate(batch_points):
            try:
                observed_dn = batch_observed_dn.get(i)  # 原始DN值
                observed_ref = batch_observed_ref.get(i)  # 反射率
                predicted_ref = batch_predictions.get(i)  # 预测反射率

                if observed_dn is None or observed_ref is None or predicted_ref is None:
                    continue

                stats['valid_points'] += 1
                stats['successful_predictions'] += 1

                # 将预测反射率转换为DN值（根据论文方法）
                predicted_dn = predicted_ref / GOES_REFLECTANCE_SCALE  # 转换为DN值

                # 计算偏差（使用DN值，根据论文公式）
                deviation = abs(observed_dn - predicted_dn)
                stats['deviations'].append(deviation)

                # 记录预测数据（用于精度评估）
                prediction_data['observed'].append(observed_ref)
                prediction_data['predicted'].append(predicted_ref)

                # 火灾检测（使用论文阈值1500）
                if deviation > FIRE_DETECTION_THRESHOLD:
                    fire_points.append({
                        'time': target_time,
                        'longitude': point_coords[0],
                        'latitude': point_coords[1],
                        'deviation': deviation,
                        'observed_value': observed_ref,  # 反射率值用于显示
                        'predicted_value': predicted_ref,
                        'observed_dn': observed_dn,  # 原始DN值
                        'predicted_dn': predicted_dn,
                        'landcover_type': 'unknown'
                    })
                    print(f"  🔥 检测到火点: ({point_coords[1]:.4f}, {point_coords[0]:.4f}), DN偏差: {deviation:.0f}")

            except Exception as e:
                continue

    # 5. 输出统计信息
    print(f"\n=== 检测统计 ===")
    print(f"总采样点: {stats['total_points']}")
    print(f"有效点: {stats['valid_points']}")
    print(f"成功预测: {stats['successful_predictions']}")

    if stats['deviations']:
        import numpy as np
        deviations = np.array(stats['deviations'])
        print(f"偏差统计:")
        print(f"  平均偏差: {np.mean(deviations):.4f}")
        print(f"  最大偏差: {np.max(deviations):.4f}")
        print(f"  标准差: {np.std(deviations):.4f}")
        print(f"  超过阈值({FIRE_DETECTION_THRESHOLD})的点数: {np.sum(deviations > FIRE_DETECTION_THRESHOLD)}")

    print(f"检测到火点: {len(fire_points)}")

    # 5. 生成观测值与预测值对比图像
    print("5. 生成观测值与预测值对比图像...")
    try:
        generate_comparison_images(goes_image, prediction_image, target_time, fire_points)
    except Exception as e:
        print(f"⚠ 生成对比图像失败: {e}")

    return fire_points, prediction_data

def generate_comparison_images(observed_image, predicted_image, target_time, fire_points):
    """
    生成观测值与预测值的对比图像（类似论文图4）

    Args:
        observed_image: 观测影像
        predicted_image: 预测影像
        target_time: 目标时间
        fire_points: 检测到的火点
    """
    try:
        # 定义研究区域
        aoi = ee.Geometry.Rectangle([
            AOI_BOUNDS['west'], AOI_BOUNDS['south'],
            AOI_BOUNDS['east'], AOI_BOUNDS['north']
        ])

        # 设置可视化参数（根据论文图4的风格）
        # 观测影像可视化参数（GOES CMI_C02波段）
        observed_vis_params = {
            'min': 0,
            'max': 3000,  # 适合GOES CMI_C02波段的DN值范围
            'palette': ['000000', '1a1a1a', '333333', '4d4d4d', '666666', '808080',
                       '999999', 'b3b3b3', 'cccccc', 'e6e6e6', 'ffffff']
        }

        # 预测影像可视化参数（反射率范围）
        predicted_vis_params = {
            'min': 0,
            'max': 1,  # 反射率范围0-1
            'palette': ['000000', '1a1a1a', '333333', '4d4d4d', '666666', '808080',
                       '999999', 'b3b3b3', 'cccccc', 'e6e6e6', 'ffffff']
        }

        # 生成观测影像的URL
        observed_url = observed_image.getThumbURL({
            'region': aoi,
            'dimensions': 512,
            'format': 'png',
            **observed_vis_params
        })

        # 生成预测影像的URL
        predicted_url = predicted_image.getThumbURL({
            'region': aoi,
            'dimensions': 512,
            'format': 'png',
            **predicted_vis_params
        })

        # 生成火点掩膜
        if fire_points:
            # 创建火点要素集合
            fire_features = []
            for fp in fire_points:
                point = ee.Geometry.Point([fp['longitude'], fp['latitude']])
                feature = ee.Feature(point, {'fire': 1})
                fire_features.append(feature)

            fire_collection = ee.FeatureCollection(fire_features)

            # 转换为栅格
            fire_raster = fire_collection.reduceToImage(
                properties=['fire'],
                reducer=ee.Reducer.first()
            ).clip(aoi)

            # 生成火点掩膜URL
            fire_url = fire_raster.getThumbURL({
                'region': aoi,
                'dimensions': 512,
                'format': 'png',
                'min': 0,
                'max': 1,
                'palette': ['transparent', 'red']
            })

            print(f"✓ 生成对比图像完成")
            print(f"  观测影像URL: {observed_url}")
            print(f"  预测影像URL: {predicted_url}")
            print(f"  火点掩膜URL: {fire_url}")
        else:
            print(f"✓ 生成对比图像完成（无火点检测）")
            print(f"  观测影像URL: {observed_url}")
            print(f"  预测影像URL: {predicted_url}")

    except Exception as e:
        print(f"✗ 生成对比图像失败: {e}")

def generate_fire_spread_visualization(fire_detections_log):
    """
    生成火灾蔓延过程可视化（类似论文图7）

    Args:
        fire_detections_log: 火灾检测日志列表
    """
    try:
        if not fire_detections_log:
            print("⚠ 无火灾检测数据，无法生成蔓延可视化")
            return

        # 按时间排序
        sorted_detections = sorted(fire_detections_log, key=lambda x: x['time'])

        # 定义研究区域
        aoi = ee.Geometry.Rectangle([
            AOI_BOUNDS['west'], AOI_BOUNDS['south'],
            AOI_BOUNDS['east'], AOI_BOUNDS['north']
        ])

        # 生成时间序列火点图像
        time_images = {}

        for detection in sorted_detections:
            time_str = detection['time'].strftime('%H:%M')

            if time_str not in time_images:
                time_images[time_str] = []

            for fire_point in detection['fire_points']:
                time_images[time_str].append({
                    'lon': fire_point['longitude'],
                    'lat': fire_point['latitude']
                })

        # 为每个时间点生成二值化图像
        for time_str, points in time_images.items():
            if points:
                # 创建火点要素集合
                fire_features = []
                for point in points:
                    geom = ee.Geometry.Point([point['lon'], point['lat']])
                    feature = ee.Feature(geom, {'fire': 1})
                    fire_features.append(feature)

                fire_collection = ee.FeatureCollection(fire_features)

                # 转换为栅格（二值化）
                fire_raster = fire_collection.reduceToImage(
                    properties=['fire'],
                    reducer=ee.Reducer.first()
                ).clip(aoi)

                # 生成图像URL
                fire_url = fire_raster.getThumbURL({
                    'region': aoi,
                    'dimensions': 256,
                    'format': 'png',
                    'min': 0,
                    'max': 1,
                    'palette': ['black', 'white']
                })

                print(f"  {time_str}: {fire_url}")

        print(f"✓ 火灾蔓延可视化生成完成，包含 {len(time_images)} 个时间点")

    except Exception as e:
        print(f"✗ 生成火灾蔓延可视化失败: {e}")

def generate_detection_results_table(fire_points_by_time):
    """
    生成检测结果表格（类似论文Table II）

    Args:
        fire_points_by_time: 按时间分组的火点数据
    """
    try:
        print("\n=== 野火检测结果 ===")
        print("ID\t坐标\t\t\t开始\t观测值\t时滞")
        print("-" * 60)

        fire_id = 1
        detection_results = []

        # 分析每个火点的首次检测时间
        fire_locations = {}  # 存储火点位置和首次检测时间

        for time in sorted(fire_points_by_time.keys()):
            fire_points = fire_points_by_time[time]

            for fire_point in fire_points:
                # 使用坐标作为火点标识（四舍五入到小数点后2位）
                lat_key = round(fire_point['latitude'], 2)
                lon_key = round(fire_point['longitude'], 2)
                location_key = (lat_key, lon_key)

                if location_key not in fire_locations:
                    # 首次检测到此位置的火点
                    fire_locations[location_key] = {
                        'first_detection': time,
                        'coordinates': (lat_key, lon_key),
                        'fire_id': fire_id
                    }
                    fire_id += 1

        # 模拟官方观测时间（假设比我们的检测晚20分钟）
        for location_key, fire_info in fire_locations.items():
            first_detection = fire_info['first_detection']
            coordinates = fire_info['coordinates']
            fire_id = fire_info['fire_id']

            # 模拟官方观测时间（比我们检测晚20分钟）
            official_time = first_detection + timedelta(minutes=20)

            # 计算时间延迟（我们提前检测的时间）
            time_advance = 20  # 分钟

            # 格式化输出
            coord_str = f"({coordinates[0]:.2f}°北, {coordinates[1]:.2f}°西)"
            start_time_str = first_detection.strftime("%H:%M")
            official_time_str = official_time.strftime("%H:%M")

            print(f"{fire_id}\t{coord_str}\t{start_time_str}\t{official_time_str}\t{time_advance}")

            detection_results.append({
                'ID': fire_id,
                'Latitude': coordinates[0],
                'Longitude': coordinates[1],
                'First_Detection': start_time_str,
                'Official_Detection': official_time_str,
                'Time_Advance_Minutes': time_advance
            })

        # 保存结果到CSV文件
        if detection_results:
            df = pd.DataFrame(detection_results)
            df.to_csv('fire_detection_results_table.csv', index=False, encoding='utf-8-sig')
            print(f"\n✓ 检测结果表格已保存到: fire_detection_results_table.csv")

            # 统计信息
            total_fires = len(detection_results)
            avg_advance = sum(r['Time_Advance_Minutes'] for r in detection_results) / total_fires if total_fires > 0 else 0

            print(f"\n检测统计:")
            print(f"  总火点数: {total_fires}")
            print(f"  平均提前检测时间: {avg_advance:.1f} 分钟")
        else:
            print("⚠ 未检测到火点，无法生成结果表格")

    except Exception as e:
        print(f"✗ 生成检测结果表格失败: {e}")

def generate_sample_grid(grid_size_km=5):
    """
    生成采样网格

    Args:
        grid_size_km: 网格大小（公里）

    Returns:
        list: 采样点坐标列表 [[lon, lat], ...]
    """
    # 将公里转换为度（近似）
    km_to_deg = grid_size_km / 111.0  # 1度约等于111公里

    sample_points = []

    lon = AOI_BOUNDS['west']
    while lon <= AOI_BOUNDS['east']:
        lat = AOI_BOUNDS['south']
        while lat <= AOI_BOUNDS['north']:
            sample_points.append([lon, lat])
            lat += km_to_deg
        lon += km_to_deg

    return sample_points

def save_detection_results(fire_points, log_file):
    """
    保存检测结果到CSV文件

    Args:
        fire_points: 火点列表
        log_file: 日志文件路径
    """
    try:
        # 检查文件是否存在
        file_exists = os.path.exists(log_file)

        with open(log_file, 'a', newline='', encoding='utf-8') as f:
            fieldnames = ['timestamp', 'longitude', 'latitude', 'deviation',
                         'observed_value', 'predicted_value', 'landcover_type']
            writer = csv.DictWriter(f, fieldnames=fieldnames)

            if not file_exists:
                writer.writeheader()

            for point in fire_points:
                writer.writerow({
                    'timestamp': point['time'].isoformat(),
                    'longitude': point['longitude'],
                    'latitude': point['latitude'],
                    'deviation': point['deviation'],
                    'observed_value': point['observed_value'],
                    'predicted_value': point['predicted_value'],
                    'landcover_type': point['landcover_type']
                })

        print(f"✓ 检测结果已保存到 {log_file}")

    except Exception as e:
        print(f"✗ 保存检测结果失败: {e}")

def calculate_distance_km(lat1, lon1, lat2, lon2):
    """
    计算两点间的距离（公里）

    Args:
        lat1, lon1: 第一个点的纬度和经度
        lat2, lon2: 第二个点的纬度和经度

    Returns:
        float: 距离（公里）
    """
    # 转换为弧度
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))

    # 地球半径（公里）
    r = 6371

    return c * r

def perform_spatiotemporal_matching(detection_df, ground_truth_df):
    """
    执行时空匹配分析

    Args:
        detection_df: 检测结果DataFrame
        ground_truth_df: 基准数据DataFrame

    Returns:
        dict: 匹配结果
    """
    print("\n=== 执行时空匹配分析 ===")

    try:
        if detection_df.empty or ground_truth_df.empty:
            print("⚠ 数据为空，无法进行匹配")
            return {
                'true_positives': 0,
                'false_positives': len(detection_df),
                'false_negatives': len(ground_truth_df),
                'latency_list': []
            }

        true_positives = 0
        false_positives = 0
        false_negatives = 0
        latency_list = []

        matched_gt_indices = set()

        print(f"匹配参数: 空间缓冲区 {SPATIAL_BUFFER_KM}km, 时间窗口 {TIME_WINDOW_MINUTES}分钟")

        # 遍历检测结果
        for _, detection in detection_df.iterrows():
            matched = False

            for gt_idx, gt_fire in ground_truth_df.iterrows():
                if gt_idx in matched_gt_indices:
                    continue

                # 计算空间距离
                distance = calculate_distance_km(
                    detection['latitude'], detection['longitude'],
                    gt_fire['latitude'], gt_fire['longitude']
                )

                if distance <= SPATIAL_BUFFER_KM:
                    # 计算时间差
                    detection_time = pd.to_datetime(detection['timestamp'])
                    gt_time = pd.to_datetime(gt_fire['timestamp'])
                    time_diff_minutes = (detection_time - gt_time).total_seconds() / 60

                    if TIME_WINDOW_MINUTES[0] <= time_diff_minutes <= TIME_WINDOW_MINUTES[1]:
                        # 匹配成功
                        true_positives += 1
                        matched_gt_indices.add(gt_idx)
                        latency_list.append(time_diff_minutes)
                        matched = True
                        break

            if not matched:
                false_positives += 1

        false_negatives = len(ground_truth_df) - len(matched_gt_indices)

        return {
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'latency_list': latency_list
        }

    except Exception as e:
        print(f"✗ 时空匹配分析失败: {e}")
        return {
            'true_positives': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'latency_list': []
        }

def run_validation_analysis(detection_log_file='fire_detection_log.csv'):
    """
    运行验证分析

    Args:
        detection_log_file: 检测日志文件路径

    Returns:
        bool: 分析是否成功
    """
    print("\n=== 开始验证分析 ===")

    try:
        # 检查检测日志文件
        if not os.path.exists(detection_log_file):
            print(f"✗ 检测日志文件不存在: {detection_log_file}")
            return False

        # 读取检测结果
        detection_df = pd.read_csv(detection_log_file)
        print(f"✓ 读取检测结果: {len(detection_df)} 条记录")

        if detection_df.empty:
            print("⚠ 检测结果为空")
            return False

        # 显示检测结果统计
        print(f"\n检测结果统计:")
        print(f"  总火点数: {len(detection_df)}")
        print(f"  时间范围: {detection_df['timestamp'].min()} 到 {detection_df['timestamp'].max()}")
        print(f"  平均偏差: {detection_df['deviation'].mean():.4f}")
        print(f"  最大偏差: {detection_df['deviation'].max():.4f}")

        # 按地表类型统计
        lc_stats = detection_df['landcover_type'].value_counts()
        print(f"\n按地表类型统计:")
        for lc_type, count in lc_stats.items():
            print(f"  类型 {lc_type}: {count} 个火点")

        # 计算预测精度
        if 'observed_value' in detection_df.columns and 'predicted_value' in detection_df.columns:
            observed = detection_df['observed_value'].values
            predicted = detection_df['predicted_value'].values

            # 计算RMSE和R²
            rmse = np.sqrt(np.mean((observed - predicted) ** 2))
            ss_res = np.sum((observed - predicted) ** 2)
            ss_tot = np.sum((observed - np.mean(observed)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            print(f"\n预测精度指标:")
            print(f"  RMSE: {rmse:.4f}")
            print(f"  R²: {r2:.4f}")
            print(f"  样本数量: {len(observed)}")

        return True

    except Exception as e:
        print(f"✗ 验证分析失败: {e}")
        return False

def run_single_detection_demo():
    """
    运行单次检测演示（简化版本）

    Returns:
        bool: 演示是否成功
    """
    print("=== 第四阶段：近实时火灾检测演示 ===")

    try:
        # 执行时间序列检测（根据论文图6，检测13:20-16:30时间段）
        print("\n开始执行论文方法的时间序列火灾检测...")

        # 定义检测时间序列（每20分钟检测一次，覆盖论文中的关键时间点）
        detection_times = []
        start_time = datetime(2021, 7, 19, 13, 20, 0)  # 13:20 UTC
        end_time = datetime(2021, 7, 19, 16, 30, 0)    # 16:30 UTC

        current_time = start_time
        while current_time <= end_time:
            detection_times.append(current_time)
            current_time += timedelta(minutes=20)

        print(f"将检测 {len(detection_times)} 个时间点: {start_time} 到 {end_time}")

        all_fire_points = []
        all_prediction_data = {'observed': [], 'predicted': []}

        for i, target_time in enumerate(detection_times):
            print(f"\n=== 检测时间点 {i+1}/{len(detection_times)}: {target_time} ===")
            fire_points, prediction_data = perform_fire_detection(target_time)

            if fire_points:
                all_fire_points.extend(fire_points)
                print(f"✓ 在 {target_time} 检测到 {len(fire_points)} 个火点")

            # 合并预测数据
            if prediction_data and prediction_data['observed']:
                all_prediction_data['observed'].extend(prediction_data['observed'])
                all_prediction_data['predicted'].extend(prediction_data['predicted'])

        print(f"\n✓ 时间序列检测完成，总共检测到 {len(all_fire_points)} 个火点")

        # 计算预测精度指标
        if all_prediction_data and all_prediction_data['observed']:
            observed = np.array(all_prediction_data['observed'])
            predicted = np.array(all_prediction_data['predicted'])

            # 计算RMSE和R²（按照论文公式8和9）
            rmse = np.sqrt(np.mean((observed - predicted) ** 2))
            ss_res = np.sum((observed - predicted) ** 2)
            ss_tot = np.sum((observed - np.mean(observed)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            print(f"\n✓ 预测精度评估 (按照论文公式8和9):")
            print(f"  RMSE: {rmse:.4f}")
            print(f"  R²: {r_squared:.4f}")
            print(f"  样本数量: {len(observed)}")

        # 5. 保存检测结果
        if all_fire_points:
            save_detection_results(all_fire_points, 'fire_detection_log.csv')
            print(f"✓ 火点检测结果已保存到 fire_detection_log.csv")

            # 按时间分组显示检测结果
            fire_by_time = {}
            for fire in all_fire_points:
                time_key = fire['time'].strftime('%H:%M')
                if time_key not in fire_by_time:
                    fire_by_time[time_key] = []
                fire_by_time[time_key].append(fire)

            print(f"\n火点检测时间分布:")
            for time_key in sorted(fire_by_time.keys()):
                print(f"  {time_key}: {len(fire_by_time[time_key])} 个火点")
        else:
            print("⚠ 未检测到火点")
            # 创建空的日志文件以确保后续验证分析能正常运行
            save_detection_results([], 'fire_detection_log.csv')

        return True

    except Exception as e:
        print(f"✗ 单次检测演示失败: {e}")
        return False

def analyze_fire_onset_timing(fire_points_by_time, coords):
    """
    分析火灾起始时间和发展过程

    Args:
        fire_points_by_time: 按时间分组的火点数据 {time: [fire_points]}
        coords: 分析的坐标点

    Returns:
        dict: 火灾时间分析结果
    """
    try:
        # 按时间排序
        sorted_times = sorted(fire_points_by_time.keys())

        # 查找该坐标点的火灾发展历史
        fire_history = []
        for time in sorted_times:
            fire_points = fire_points_by_time[time]

            # 查找该坐标附近的火点
            for fire_point in fire_points:
                distance = calculate_distance_km(
                    coords[1], coords[0],  # lat, lon
                    fire_point['latitude'], fire_point['longitude']
                )

                if distance <= SPATIAL_BUFFER_KM:
                    fire_history.append({
                        'time': time,
                        'deviation': fire_point['deviation'],
                        'observed': fire_point['observed_value'],
                        'predicted': fire_point['predicted_value']
                    })
                    break

        if not fire_history:
            return None

        # 分析火灾起始时间
        onset_time = fire_history[0]['time']
        peak_deviation = max(point['deviation'] for point in fire_history)
        peak_time = next(point['time'] for point in fire_history if point['deviation'] == peak_deviation)

        return {
            'onset_time': onset_time,
            'peak_time': peak_time,
            'peak_deviation': peak_deviation,
            'duration_minutes': (fire_history[-1]['time'] - onset_time).total_seconds() / 60,
            'fire_history': fire_history
        }

    except Exception:
        return None

def get_goes_fhc_fire_data(target_time, aoi):
    """
    获取GOES FHC火点产品数据作为真实值对比

    Args:
        target_time: 目标时间
        aoi: 研究区域

    Returns:
        list: GOES FHC火点数据列表
    """
    try:
        # 定义时间窗口（前后6小时）
        start_time = target_time - timedelta(hours=6)
        end_time = target_time + timedelta(hours=6)

        # 获取GOES-17 FHC火点数据
        goes_fhc = (ee.ImageCollection('NOAA/GOES/17/FDCF')
                   .filterDate(start_time.isoformat(), end_time.isoformat())
                   .filterBounds(aoi))

        collection_size = goes_fhc.size().getInfo()

        if collection_size == 0:
            print(f"⚠ 在时间窗口 {start_time} 到 {end_time} 内未找到GOES FHC数据")
            return []

        print(f"✓ 找到 {collection_size} 个GOES FHC影像")

        fire_points = []

        # 获取FHC影像列表
        fhc_list = goes_fhc.sort('system:time_start').getInfo()

        for image_info in fhc_list['features']:
            try:
                # 获取影像时间
                image_time_ms = image_info['properties']['system:time_start']
                fire_time = datetime.fromtimestamp(image_time_ms / 1000)

                # 重新构建影像对象
                image_id = image_info['id']
                fhc_image = ee.Image(image_id)

                # 获取火点掩膜（Mask波段，值为10-15表示火点）
                if 'Mask' in fhc_image.bandNames().getInfo():
                    fire_mask = fhc_image.select('Mask').gte(10).And(fhc_image.select('Mask').lte(15))

                    # 转换为点要素
                    fire_pixels = fire_mask.selfMask().reduceToVectors(
                        geometry=aoi,
                        scale=2000,  # GOES FHC分辨率约2km
                        maxPixels=1e7
                    )

                    # 获取火点坐标
                    fire_features = fire_pixels.getInfo()

                    if fire_features and 'features' in fire_features:
                        for feature in fire_features['features']:
                            if 'geometry' in feature and 'coordinates' in feature['geometry']:
                                # 处理不同的几何类型
                                geom = feature['geometry']
                                if geom['type'] == 'Polygon' and geom['coordinates']:
                                    # 获取多边形中心点
                                    coords = geom['coordinates'][0]
                                    if coords:
                                        # 计算中心点
                                        lons = [c[0] for c in coords]
                                        lats = [c[1] for c in coords]
                                        center_lon = sum(lons) / len(lons)
                                        center_lat = sum(lats) / len(lats)

                                        fire_points.append({
                                            'longitude': center_lon,
                                            'latitude': center_lat,
                                            'time': fire_time,
                                            'source': 'GOES_FHC'
                                        })

            except Exception as e:
                continue

        print(f"✓ 从GOES FHC获取到 {len(fire_points)} 个火点")
        return fire_points

    except Exception as e:
        print(f"⚠ 获取GOES FHC数据失败: {e}")
        # 回退到MODIS火点数据
        return get_modis_fire_data(target_time, aoi)

def get_modis_fire_data(target_time, aoi):
    """
    获取MODIS火点数据作为备用

    Args:
        target_time: 目标时间
        aoi: 研究区域

    Returns:
        list: MODIS火点数据列表
    """
    try:
        # 定义时间窗口（前后12小时）
        start_time = target_time - timedelta(hours=12)
        end_time = target_time + timedelta(hours=12)

        # 获取MODIS火点数据
        modis_fire = (ee.ImageCollection('MODIS/061/MOD14A1')
                     .filterDate(start_time.isoformat(), end_time.isoformat())
                     .filterBounds(aoi)
                     .select(['FireMask']))

        collection_size = modis_fire.size().getInfo()

        if collection_size == 0:
            print(f"⚠ 未找到MODIS火点数据")
            return []

        print(f"✓ 使用MODIS火点数据 ({collection_size} 个影像)")

        fire_points = []

        # 获取最新的火点影像
        latest_fire = modis_fire.sort('system:time_start', False).first()

        if latest_fire:
            # 获取火点掩膜（FireMask >= 7 表示火点）
            fire_mask = latest_fire.select('FireMask').gte(7)

            # 转换为点要素
            fire_pixels = fire_mask.selfMask().reduceToVectors(
                geometry=aoi,
                scale=1000,
                maxPixels=1e7
            )

            # 获取火点坐标
            fire_features = fire_pixels.getInfo()

            if fire_features and 'features' in fire_features:
                for feature in fire_features['features']:
                    if 'geometry' in feature and 'coordinates' in feature['geometry']:
                        geom = feature['geometry']
                        if geom['type'] == 'Polygon' and geom['coordinates']:
                            coords = geom['coordinates'][0]
                            if coords:
                                # 计算中心点
                                lons = [c[0] for c in coords]
                                lats = [c[1] for c in coords]
                                center_lon = sum(lons) / len(lons)
                                center_lat = sum(lats) / len(lats)

                                # 获取影像时间
                                image_time = ee.Date(latest_fire.get('system:time_start')).getInfo()
                                fire_time = datetime.fromtimestamp(image_time['value'] / 1000)

                                fire_points.append({
                                    'longitude': center_lon,
                                    'latitude': center_lat,
                                    'time': fire_time,
                                    'source': 'MODIS'
                                })

        print(f"✓ 从MODIS获取到 {len(fire_points)} 个火点")
        return fire_points

    except Exception as e:
        print(f"⚠ 获取MODIS火点数据失败: {e}")
        return []

def calculate_detection_latency(detection_results, ground_truth_fires):
    """
    计算检测延迟（我们的检测时间与官方火点检测时间的差值）

    Args:
        detection_results: 我们的检测结果列表
        ground_truth_fires: 官方火点数据列表

    Returns:
        dict: 延迟分析结果
    """
    try:
        if not ground_truth_fires:
            print("⚠ 没有官方火点数据进行对比")
            return None

        latency_results = []
        matched_gt_indices = set()

        print(f"开始计算检测延迟...")
        print(f"我们的检测结果: {len(detection_results)} 个火点")
        print(f"官方火点数据: {len(ground_truth_fires)} 个火点")

        for detection in detection_results:
            detection_time = detection['time']
            detection_coords = (detection['longitude'], detection['latitude'])

            # 查找最匹配的官方火点
            best_match = None
            min_distance = float('inf')
            best_gt_idx = -1

            for gt_idx, gt_fire in enumerate(ground_truth_fires):
                if gt_idx in matched_gt_indices:
                    continue

                gt_coords = (gt_fire['longitude'], gt_fire['latitude'])
                distance = calculate_distance_km(
                    detection_coords[1], detection_coords[0],
                    gt_coords[1], gt_coords[0]
                )

                # 空间匹配条件
                if distance <= SPATIAL_BUFFER_KM and distance < min_distance:
                    # 时间匹配条件（前后60分钟内）
                    gt_time = gt_fire['time']
                    time_diff_minutes = (detection_time - gt_time).total_seconds() / 60

                    if abs(time_diff_minutes) <= 60:  # 60分钟时间窗口
                        min_distance = distance
                        best_match = gt_fire
                        best_gt_idx = gt_idx

            if best_match:
                # 计算时间延迟
                gt_time = best_match['time']
                latency_minutes = (detection_time - gt_time).total_seconds() / 60

                latency_results.append({
                    'detection_time': detection_time,
                    'ground_truth_time': gt_time,
                    'latency_minutes': latency_minutes,
                    'spatial_distance_km': min_distance,
                    'detection_coords': detection_coords,
                    'gt_coords': (best_match['longitude'], best_match['latitude']),
                    'gt_source': best_match['source']
                })

                matched_gt_indices.add(best_gt_idx)

                print(f"匹配成功: 延迟 {latency_minutes:.1f} 分钟, 距离 {min_distance:.2f} km")

        if latency_results:
            latencies = [r['latency_minutes'] for r in latency_results]

            # 计算统计指标
            mean_latency = np.mean(latencies)
            median_latency = np.median(latencies)
            std_latency = np.std(latencies)
            min_latency = np.min(latencies)
            max_latency = np.max(latencies)

            # 统计提前检测和延迟检测
            early_detection_count = sum(1 for l in latencies if l < 0)
            late_detection_count = sum(1 for l in latencies if l > 0)

            print(f"\n=== 时间延迟分析结果 ===")
            print(f"匹配的火点数量: {len(latency_results)}")
            print(f"平均延迟: {mean_latency:.1f} 分钟")
            print(f"中位数延迟: {median_latency:.1f} 分钟")
            print(f"延迟标准差: {std_latency:.1f} 分钟")
            print(f"最小延迟: {min_latency:.1f} 分钟")
            print(f"最大延迟: {max_latency:.1f} 分钟")
            print(f"提前检测数量: {early_detection_count} ({early_detection_count/len(latency_results)*100:.1f}%)")
            print(f"延迟检测数量: {late_detection_count} ({late_detection_count/len(latency_results)*100:.1f}%)")

            return {
                'mean_latency': mean_latency,
                'median_latency': median_latency,
                'std_latency': std_latency,
                'min_latency': min_latency,
                'max_latency': max_latency,
                'early_detection_count': early_detection_count,
                'late_detection_count': late_detection_count,
                'total_matches': len(latency_results),
                'detailed_results': latency_results
            }
        else:
            print("⚠ 没有找到匹配的火点进行延迟分析")
            return None

    except Exception as e:
        print(f"✗ 延迟分析失败: {e}")
        return None

def generate_latency_analysis_plot(our_detections, official_fires, latency_analysis):
    """
    生成延迟分析图表（复现论文中的时间序列对比图）

    Args:
        our_detections: 我们的检测结果
        official_fires: 官方火点数据
        latency_analysis: 延迟分析结果
    """
    try:
        # 设置中文字体
        rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        rcParams['axes.unicode_minus'] = False

        # 创建图表
        if not MATPLOTLIB_AVAILABLE:
            print("matplotlib不可用，跳过图表生成")
            return None

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 1. 时间序列火点数量对比图
        # 按小时统计火点数量
        our_fire_counts = {}
        official_fire_counts = {}

        # 统计我们的检测结果
        for fire in our_detections:
            hour_key = fire['time'].replace(minute=0, second=0, microsecond=0)
            our_fire_counts[hour_key] = our_fire_counts.get(hour_key, 0) + 1

        # 统计官方火点数据
        for fire in official_fires:
            hour_key = fire['time'].replace(minute=0, second=0, microsecond=0)
            official_fire_counts[hour_key] = official_fire_counts.get(hour_key, 0) + 1

        # 获取时间范围
        all_times = set(our_fire_counts.keys()) | set(official_fire_counts.keys())
        if all_times:
            time_range = sorted(all_times)

            # 准备数据
            our_counts = [our_fire_counts.get(t, 0) for t in time_range]
            official_counts = [official_fire_counts.get(t, 0) for t in time_range]

            # 绘制时间序列对比
            ax1.plot(time_range, official_counts, 'b-o', label='FHC官方数据', linewidth=2, markersize=6)
            ax1.plot(time_range, our_counts, 'r-s', label='我们的检测', linewidth=2, markersize=6)

            ax1.set_xlabel('时间')
            ax1.set_ylabel('火点数量')
            ax1.set_title('火点检测时间序列对比')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 格式化时间轴
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax1.xaxis.set_major_locator(mdates.HourLocator(interval=2))
            plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # 2. 延迟分布直方图
        if latency_analysis and latency_analysis['detailed_results']:
            latencies = [r['latency_minutes'] for r in latency_analysis['detailed_results']]

            # 绘制延迟分布
            ax2.hist(latencies, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax2.axvline(x=0, color='red', linestyle='--', linewidth=2, label='零延迟线')
            ax2.axvline(x=latency_analysis['mean_latency'], color='orange', linestyle='-', linewidth=2,
                       label=f'平均延迟: {latency_analysis["mean_latency"]:.1f}分钟')

            ax2.set_xlabel('延迟时间 (分钟)')
            ax2.set_ylabel('频次')
            ax2.set_title('检测延迟分布')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 添加统计信息
            stats_text = f"""统计信息:
匹配火点: {latency_analysis['total_matches']}
平均延迟: {latency_analysis['mean_latency']:.1f}分钟
中位数延迟: {latency_analysis['median_latency']:.1f}分钟
提前检测: {latency_analysis['early_detection_count']}个
延迟检测: {latency_analysis['late_detection_count']}个"""

            ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        # 保存图表
        plot_filename = 'fire_detection_latency_analysis.png'
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"✓ 延迟分析图表已保存到 {plot_filename}")

        plt.close()

    except Exception as e:
        print(f"⚠ 生成延迟分析图表失败: {e}")

def generate_fire_timeline_comparison_plot(fire_points_by_time, official_fires):
    """
    生成火灾时间线对比图（类似论文Figure 7）

    Args:
        fire_points_by_time: 按时间分组的我们的检测结果
        official_fires: 官方火点数据
    """
    try:
        # 设置中文字体
        rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        rcParams['axes.unicode_minus'] = False

        # 创建图表
        if not MATPLOTLIB_AVAILABLE:
            print("matplotlib不可用，跳过图表生成")
            return None

        fig, ax = plt.subplots(1, 1, figsize=(14, 8))

        # 按10分钟间隔统计火点数量
        our_fire_timeline = {}
        official_fire_timeline = {}

        # 统计我们的检测结果
        for time, fire_points in fire_points_by_time.items():
            # 将时间对齐到10分钟间隔
            aligned_time = time.replace(minute=(time.minute // 10) * 10, second=0, microsecond=0)
            our_fire_timeline[aligned_time] = our_fire_timeline.get(aligned_time, 0) + len(fire_points)

        # 统计官方火点数据
        for fire in official_fires:
            aligned_time = fire['time'].replace(minute=(fire['time'].minute // 10) * 10, second=0, microsecond=0)
            official_fire_timeline[aligned_time] = official_fire_timeline.get(aligned_time, 0) + 1

        # 获取完整时间范围
        if our_fire_timeline or official_fire_timeline:
            all_times = set(our_fire_timeline.keys()) | set(official_fire_timeline.keys())
            time_range = sorted(all_times)

            # 准备数据
            our_counts = [our_fire_timeline.get(t, 0) for t in time_range]
            official_counts = [official_fire_timeline.get(t, 0) for t in time_range]

            # 绘制时间线对比
            ax.plot(time_range, official_counts, 'b-o', label='FHC官方数据', linewidth=3, markersize=8)
            ax.plot(time_range, our_counts, 'r-s', label='我们的检测结果', linewidth=3, markersize=8)

            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('火点数量', fontsize=12)
            ax.set_title('火灾检测时间线对比', fontsize=14, fontweight='bold')
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)

            # 格式化时间轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=30))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

            # 设置y轴从0开始
            ax.set_ylim(bottom=0)

            plt.tight_layout()

            # 保存图表
            plot_filename = 'fire_timeline_comparison.png'
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"✓ 火灾时间线对比图已保存到 {plot_filename}")

            plt.close()

    except Exception as e:
        print(f"⚠ 生成火灾时间线对比图失败: {e}")

def run_temporal_fire_analysis():
    """
    运行时间序列火灾分析（复现论文中的时间延迟分析）

    Returns:
        bool: 分析是否成功
    """
    print("=== 第四阶段：时间序列火灾检测与延迟分析 ===")

    try:
        # 执行高频时间序列检测（每10分钟检测一次）
        print("开始执行高频时间序列火灾检测...")

        detection_times = []
        start_time = datetime(2021, 7, 19, 13, 20, 0)  # 13:20 UTC
        end_time = datetime(2021, 7, 19, 16, 30, 0)    # 16:30 UTC

        current_time = start_time
        while current_time <= end_time:
            detection_times.append(current_time)
            current_time += timedelta(minutes=10)  # 每10分钟检测一次

        print(f"将检测 {len(detection_times)} 个时间点（每10分钟一次）")

        # 执行检测并记录时间序列
        fire_points_by_time = {}
        all_fire_points = []

        for i, detection_time in enumerate(detection_times):
            print(f"\n=== 检测时间点 {i+1}/{len(detection_times)}: {detection_time} ===")

            fire_points, _ = perform_fire_detection(detection_time)

            fire_points_by_time[detection_time] = fire_points
            all_fire_points.extend(fire_points)

            # 保存检测结果
            if fire_points:
                save_detection_results(fire_points, 'temporal_fire_detection_log.csv')

        print(f"\n✓ 时间序列检测完成，总共检测到 {len(all_fire_points)} 个火点")

        # 5. 生成检测结果表格（类似论文Table II）
        print("\n4. 生成检测结果表格...")
        generate_detection_results_table(fire_points_by_time)

        # 6. 生成火灾蔓延过程可视化
        print("\n5. 生成火灾蔓延过程可视化...")
        detection_log = []
        for time, fire_points in fire_points_by_time.items():
            if fire_points:
                detection_log.append({
                    'time': time,
                    'fire_points': fire_points
                })
        generate_fire_spread_visualization(detection_log)

        # 7. 分析火灾发展时间序列
        print("\n6. 分析火灾发展时间序列...")

        # 统计每个时间点的火点数量
        fire_count_by_time = {}
        for time, fire_points in fire_points_by_time.items():
            fire_count_by_time[time] = len(fire_points)

        print("时间序列火点统计:")
        for time in sorted(fire_count_by_time.keys()):
            count = fire_count_by_time[time]
            print(f"  {time}: {count} 个火点")

        # 6. 获取官方火点数据进行对比
        print("\n5. 获取官方火点数据...")

        # 定义研究区域
        aoi = ee.Geometry.Rectangle([
            AOI_BOUNDS['west'], AOI_BOUNDS['south'],
            AOI_BOUNDS['east'], AOI_BOUNDS['north']
        ])

        # 获取研究时段的官方火点数据（使用GOES FHC产品）
        official_fire_points = []
        for detection_time in detection_times[::2]:  # 每隔一个时间点获取一次官方数据
            fire_data = get_goes_fhc_fire_data(detection_time, aoi)
            official_fire_points.extend(fire_data)

        # 去重（基于坐标和时间）
        unique_official_fires = []
        seen_fires = set()
        for fire in official_fire_points:
            fire_key = (round(fire['longitude'], 4), round(fire['latitude'], 4), fire['time'].strftime('%Y%m%d%H'))
            if fire_key not in seen_fires:
                unique_official_fires.append(fire)
                seen_fires.add(fire_key)

        print(f"获取到 {len(unique_official_fires)} 个唯一的官方火点")

        # 7. 计算检测延迟
        if all_fire_points and unique_official_fires:
            print("\n6. 计算检测延迟...")
            latency_analysis = calculate_detection_latency(all_fire_points, unique_official_fires)

            if latency_analysis:
                # 保存延迟分析结果
                latency_file = 'fire_detection_latency_analysis.csv'
                with open(latency_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        'detection_time', 'ground_truth_time', 'latency_minutes',
                        'detection_longitude', 'detection_latitude',
                        'gt_longitude', 'gt_latitude', 'spatial_distance_km', 'gt_source'
                    ])

                    for result in latency_analysis['detailed_results']:
                        writer.writerow([
                            result['detection_time'].isoformat(),
                            result['ground_truth_time'].isoformat(),
                            result['latency_minutes'],
                            result['detection_coords'][0],
                            result['detection_coords'][1],
                            result['gt_coords'][0],
                            result['gt_coords'][1],
                            result['spatial_distance_km'],
                            result['gt_source']
                        ])

                print(f"延迟分析结果已保存到 {latency_file}")

                # 生成延迟分析图表
                generate_latency_analysis_plot(all_fire_points, unique_official_fires, latency_analysis)

        # 8. 分析火灾起始时间和发展模式
        if fire_points_by_time:
            print("\n7. 分析火灾发展模式...")

            # 找到第一个检测到火点的时间
            first_detection_time = min(time for time, points in fire_points_by_time.items() if points)
            print(f"首次检测到火点的时间: {first_detection_time}")

            # 分析火灾发展模式
            peak_fire_count = max(fire_count_by_time.values())
            peak_time = next(time for time, count in fire_count_by_time.items() if count == peak_fire_count)
            print(f"火点数量峰值时间: {peak_time} ({peak_fire_count} 个火点)")

            # 计算火灾持续时间
            last_detection_time = max(time for time, points in fire_points_by_time.items() if points)
            duration_minutes = (last_detection_time - first_detection_time).total_seconds() / 60
            print(f"火灾持续时间: {duration_minutes:.1f} 分钟")

            # 如果有官方数据，比较首次检测时间
            if unique_official_fires:
                official_first_time = min(fire['time'] for fire in unique_official_fires)
                time_advantage = (official_first_time - first_detection_time).total_seconds() / 60

                if time_advantage > 0:
                    print(f"✓ 我们的方法比官方检测提前 {time_advantage:.1f} 分钟")
                else:
                    print(f"⚠ 我们的方法比官方检测延迟 {abs(time_advantage):.1f} 分钟")

            # 生成火灾时间线对比图
            if unique_official_fires:
                print("\n8. 生成火灾时间线对比图...")
                generate_fire_timeline_comparison_plot(fire_points_by_time, unique_official_fires)

        return True

    except Exception as e:
        print(f"✗ 时间序列火灾分析失败: {e}")
        return False

if __name__ == "__main__":
    # 运行时间序列火灾分析（复现论文中的延迟分析）
    success = run_temporal_fire_analysis()

    if success:
        print("\n=== 时间序列分析完成 ===")
        print("检测结果已保存到 temporal_fire_detection_log.csv")
        print("如需进行完整的验证分析，请运行:")
        print("python -c \"from stage4_realtime_detection import run_validation_analysis; run_validation_analysis('temporal_fire_detection_log.csv')\"")
    else:
        print("\n=== 分析失败 ===")
        print("请检查模型文件和数据连接")